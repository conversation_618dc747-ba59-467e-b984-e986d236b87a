package com.supermap;

import com.supermap.tools.base.RedisCommonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * 该类用来进行系统启动后的初始化工作
 */
@Configuration
@EnableScheduling
public class ApplicationStartup implements ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    private RedisCommonUtil _redisCommonUtil;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {

    }

    /**
     * 初始化Redis的变量值
     */
    //@Scheduled(fixedRate = 1000)      ----这行代码开启后将会启用定时器功能
    public void initRedisValues(){
        //初始化并行执行的进程/线程数为0
        _redisCommonUtil.setKeyAndTime("parallelprocesscount", "0", 1, TimeUnit.HOURS);
    }

    /**
     * 在项目启动时进行的环境初始化操作
     * (只在系统启动时执行一次)
     */
    @PostConstruct
    public void prepareEnvironments() {
        initRedisValues();
    }
}
